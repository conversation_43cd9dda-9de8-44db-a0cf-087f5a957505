import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Cook<PERSON>, Settings, X, Shield, BarChart3, Zap, Target } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface CookiePreferences {
  essential: boolean;
  analytics: boolean;
  functional: boolean;
  marketing: boolean;
}

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true,
    analytics: false,
    functional: false,
    marketing: false
  });

  // Add a global function to reset cookie consent for testing
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).resetCookieConsent = () => {
        localStorage.removeItem('cookieConsent');
        setShowBanner(true);
        console.log('Cookie consent reset - banner will show');
      };
    }
  }, []);

  useEffect(() => {
    // Check if user has already made a choice
    const cookieConsent = localStorage.getItem('cookieConsent');
    if (!cookieConsent) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      try {
        const savedPreferences = JSON.parse(cookieConsent);
        setPreferences(savedPreferences);
        applyCookieSettings(savedPreferences);
      } catch (error) {
        console.error('Error parsing cookie preferences:', error);
      }
    }
  }, []);

  const applyCookieSettings = (prefs: CookiePreferences) => {
    // Apply analytics cookies
    if (prefs.analytics) {
      // Store consent for the analytics loader
      localStorage.setItem('analytics-consent', 'true');

      // Enable Google Analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          analytics_storage: 'granted'
        });
      }

      // Dispatch consent change event for dynamic loading
      window.dispatchEvent(new CustomEvent('consentchange', {
        detail: { type: 'analytics', granted: true }
      }));
    } else {
      // Remove consent for the analytics loader
      localStorage.setItem('analytics-consent', 'false');

      // Disable Google Analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          analytics_storage: 'denied'
        });
      }

      // Dispatch consent change event
      window.dispatchEvent(new CustomEvent('consentchange', {
        detail: { type: 'analytics', granted: false }
      }));
    }

    // Apply marketing cookies
    if (prefs.marketing) {
      // Enable marketing cookies
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          ad_storage: 'granted'
        });
      }
    } else {
      // Disable marketing cookies
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          ad_storage: 'denied'
        });
      }
    }

    // Apply functional cookies (for features like chat widgets, etc.)
    if (prefs.functional) {
      // Enable functional cookies
      document.cookie = 'functional_cookies=enabled; path=/; max-age=31536000';
    } else {
      // Disable functional cookies
      document.cookie = 'functional_cookies=disabled; path=/; max-age=31536000';
    }
  };

  const savePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem('cookieConsent', JSON.stringify(prefs));
    applyCookieSettings(prefs);
    setPreferences(prefs);
    setShowBanner(false);
    setShowSettings(false);

    // If analytics is enabled, load Google Analytics dynamically
    if (prefs.analytics && typeof window !== 'undefined') {
      import('../../../utils/thirdPartyOptimization.js').then(({ loadGoogleAnalytics }) => {
        const measurementId = import.meta.env.VITE_GA_MEASUREMENT_ID;
        if (measurementId) {
          loadGoogleAnalytics(measurementId);
        }
      });
    }
  };

  const acceptAll = () => {
    const allAccepted = {
      essential: true,
      analytics: true,
      functional: true,
      marketing: true
    };
    savePreferences(allAccepted);
  };

  const acceptEssential = () => {
    const essentialOnly = {
      essential: true,
      analytics: false,
      functional: false,
      marketing: false
    };
    savePreferences(essentialOnly);
  };

  const handlePreferenceChange = (type: keyof CookiePreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [type]: value
    }));
  };

  const saveCustomPreferences = () => {
    savePreferences(preferences);
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Luxury Cookie Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 luxury-glass-container border-t border-[#D4C2A4]/30 shadow-2xl cookie-banner-enter">
        {/* Luxury Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/95 via-[#16191D]/90 to-[#16191D]/85"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/5 via-transparent to-[#D4C2A4]/10"></div>

        {/* Floating Luxury Elements */}
        <div className="absolute top-4 left-8 w-1 h-1 bg-[#D4C2A4]/40 rounded-full luxury-particle"></div>
        <div className="absolute top-6 right-12 w-0.5 h-0.5 bg-[#D4C2A4]/30 rounded-full luxury-particle"></div>

        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6 lg:gap-8">
            <div className="flex items-start space-x-4 sm:space-x-6 flex-1">
              <div className="relative">
                <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-lg"></div>
                <Cookie className="relative h-8 w-8 sm:h-10 sm:w-10 text-[#D4C2A4] mt-1 flex-shrink-0" />
              </div>
              <div className="flex-1">
                <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-semibold text-[#F2EEE6] mb-2 sm:mb-3 luxury-typography tracking-wide">
                  Premium Cookie Experience
                </h3>
                <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed max-w-2xl font-light tracking-wide">
                  We use premium cookies to enhance your luxury safari browsing experience, analyze site performance, and provide personalized content.
                  Your privacy is our priority in delivering exceptional service.
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full lg:w-auto lg:flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(true)}
                className="luxury-button bg-transparent border-[#D4C2A4]/40 text-black hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/60 transition-all duration-300 font-open-sans font-medium text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3 tracking-wide"
              >
                <Settings className="h-4 w-4 mr-2" />
                Customize
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={acceptEssential}
                className="luxury-button bg-transparent border-[#D4C2A4]/40 text-black hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/60 transition-all duration-300 font-open-sans font-medium text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3 tracking-wide"
              >
                Essential Only
              </Button>
              <Button
                size="sm"
                onClick={acceptAll}
                className="luxury-button bg-gradient-to-r from-[#D4C2A4] to-[#C4B294] hover:from-[#C4B294] hover:to-[#D4C2A4] text-black hover:text-[#16191D] font-semibold transition-all duration-300 font-open-sans text-sm sm:text-base px-6 sm:px-8 py-2 sm:py-3 shadow-lg hover:shadow-xl tracking-wide"
              >
                Accept All
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Luxury Cookie Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-[#16191D] border border-[#D4C2A4]/30 luxury-glass-container cookie-dialog-enter">
          {/* Luxury Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#16191D] via-[#1a1e23] to-[#16191D]"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/5 to-transparent"></div>

          {/* Floating Luxury Elements */}
          <div className="absolute top-6 left-8 w-1 h-1 bg-[#D4C2A4]/40 rounded-full luxury-particle"></div>
          <div className="absolute top-12 right-16 w-0.5 h-0.5 bg-[#D4C2A4]/30 rounded-full luxury-particle"></div>
          <div className="absolute bottom-8 left-12 w-0.5 h-0.5 bg-[#D4C2A4]/25 rounded-full luxury-particle"></div>

          <div className="relative z-10">
            <DialogHeader className="pb-6">
              <DialogTitle className="flex items-center text-2xl sm:text-3xl lg:text-4xl font-cormorant font-semibold text-[#F2EEE6] luxury-typography tracking-wide">
                <div className="relative mr-4">
                  <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-lg"></div>
                  <Cookie className="relative h-8 w-8 sm:h-10 sm:w-10 text-[#D4C2A4]" />
                </div>
                Premium Cookie Preferences
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-8">
              <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-xl p-6">
                <p className="font-open-sans text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 leading-relaxed font-light tracking-wide">
                  Customize your premium safari experience. Essential cookies ensure optimal website functionality and cannot be disabled.
                  Other preferences can be adjusted to match your privacy requirements while maintaining our luxury service standards.
                </p>
              </div>

              {/* Essential Cookies */}
              <Card className="cookie-card bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-xl overflow-hidden">
                <CardHeader className="pb-4 bg-gradient-to-r from-[#D4C2A4]/10 to-transparent">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-md"></div>
                        <Shield className="relative h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <CardTitle className="text-base sm:text-lg lg:text-xl font-cormorant font-semibold text-[#F2EEE6] luxury-typography tracking-wide">
                        Essential Cookies
                      </CardTitle>
                    </div>
                    <Switch
                      checked={true}
                      disabled
                      className="cookie-switch data-[state=checked]:bg-[#D4C2A4] data-[state=unchecked]:bg-[#D4C2A4]/20 scale-90 sm:scale-100"
                    />
                  </div>
                </CardHeader>
                <CardContent className="pt-0 px-4 sm:px-6 pb-4 sm:pb-6">
                  <p className="font-open-sans text-xs sm:text-sm lg:text-base text-[#F2EEE6]/70 leading-relaxed font-light tracking-wide">
                    These premium cookies are essential for our luxury safari website to function properly and provide you with the best possible experience.
                    They cannot be disabled as they ensure security, authentication, and core functionality.
                  </p>
                </CardContent>
              </Card>

              {/* Analytics Cookies */}
              <Card className="cookie-card bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-xl overflow-hidden">
                <CardHeader className="pb-4 bg-gradient-to-r from-[#D4C2A4]/10 to-transparent">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-md"></div>
                        <BarChart3 className="relative h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <CardTitle className="text-base sm:text-lg lg:text-xl font-cormorant font-semibold text-[#F2EEE6] luxury-typography tracking-wide">
                        Analytics Cookies
                      </CardTitle>
                    </div>
                    <Switch
                      checked={preferences.analytics}
                      onCheckedChange={(checked) => handlePreferenceChange('analytics', checked)}
                      className="cookie-switch data-[state=checked]:bg-[#D4C2A4] data-[state=unchecked]:bg-[#D4C2A4]/20 scale-90 sm:scale-100"
                    />
                  </div>
                </CardHeader>
                <CardContent className="pt-0 px-4 sm:px-6 pb-4 sm:pb-6">
                  <p className="font-open-sans text-xs sm:text-sm lg:text-base text-[#F2EEE6]/70 leading-relaxed font-light tracking-wide">
                    These cookies help us understand how our distinguished guests interact with our premium safari website,
                    allowing us to continuously improve your luxury browsing experience through anonymous data collection.
                  </p>
                </CardContent>
              </Card>

              {/* Functional Cookies */}
              <Card className="cookie-card bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-xl overflow-hidden">
                <CardHeader className="pb-4 bg-gradient-to-r from-[#D4C2A4]/10 to-transparent">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-md"></div>
                        <Zap className="relative h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <CardTitle className="text-base sm:text-lg lg:text-xl font-cormorant font-semibold text-[#F2EEE6] luxury-typography tracking-wide">
                        Functional Cookies
                      </CardTitle>
                    </div>
                    <Switch
                      checked={preferences.functional}
                      onCheckedChange={(checked) => handlePreferenceChange('functional', checked)}
                      className="cookie-switch data-[state=checked]:bg-[#D4C2A4] data-[state=unchecked]:bg-[#D4C2A4]/20 scale-90 sm:scale-100"
                    />
                  </div>
                </CardHeader>
                <CardContent className="pt-0 px-4 sm:px-6 pb-4 sm:pb-6">
                  <p className="font-open-sans text-xs sm:text-sm lg:text-base text-[#F2EEE6]/70 leading-relaxed font-light tracking-wide">
                    These cookies enable enhanced luxury features and personalization, including premium chat support,
                    social media integration, and customized safari recommendations tailored to your preferences.
                  </p>
                </CardContent>
              </Card>

              {/* Marketing Cookies */}
              <Card className="cookie-card bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-xl overflow-hidden">
                <CardHeader className="pb-4 bg-gradient-to-r from-[#D4C2A4]/10 to-transparent">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-md"></div>
                        <Target className="relative h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <CardTitle className="text-base sm:text-lg lg:text-xl font-cormorant font-semibold text-[#F2EEE6] luxury-typography tracking-wide">
                        Marketing Cookies
                      </CardTitle>
                    </div>
                    <Switch
                      checked={preferences.marketing}
                      onCheckedChange={(checked) => handlePreferenceChange('marketing', checked)}
                      className="cookie-switch data-[state=checked]:bg-[#D4C2A4] data-[state=unchecked]:bg-[#D4C2A4]/20 scale-90 sm:scale-100"
                    />
                  </div>
                </CardHeader>
                <CardContent className="pt-0 px-4 sm:px-6 pb-4 sm:pb-6">
                  <p className="font-open-sans text-xs sm:text-sm lg:text-base text-[#F2EEE6]/70 leading-relaxed font-light tracking-wide">
                    These cookies help us deliver personalized luxury safari advertisements and exclusive offers,
                    while measuring the effectiveness of our premium marketing campaigns to better serve discerning travelers.
                  </p>
                </CardContent>
              </Card>

              <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-8">
                <Button
                  variant="outline"
                  onClick={() => setShowSettings(false)}
                  className="luxury-button bg-transparent border-[#D4C2A4]/40 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/60 transition-all duration-300 font-open-sans font-medium px-6 py-3 tracking-wide"
                >
                  Cancel
                </Button>
                <Button
                  onClick={saveCustomPreferences}
                  className="luxury-button bg-gradient-to-r from-[#D4C2A4] to-[#C4B294] hover:from-[#C4B294] hover:to-[#D4C2A4] text-[#16191D] hover:text-[#16191D] font-semibold transition-all duration-300 font-open-sans px-8 py-3 shadow-lg hover:shadow-xl tracking-wide"
                >
                  Save Preferences
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CookieConsent;
